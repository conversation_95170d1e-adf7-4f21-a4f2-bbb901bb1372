#!/usr/bin/env python3

from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from typing import List, Tuple

from app.models.staking import StakingAccount

from ...common import PrecisionEnum
from ...business import SPOT_ACCOUNT_ID, ExchangeLogDB
from ...common import AccountBalanceType
from ...models import AssetInvestmentConfig, SubAccount, User, UserLiquiditySlice
from ...utils import today, today_timestamp_utc, quantize_amount


def _format_series(series: List[Tuple[date, Decimal]], 
                   start: date,
                   points: int) -> List[Tuple[date, Decimal]]:
    result = []
    index = 0
    # skip premature points
    while index < len(series):
        if series[index][0] >= start:
            break
        index += 1
    for i in range(points):
        d = start + timedelta(days=i)
        if index < len(series) and d == series[index][0]:
            result.append((d, quantize_amount(series[index][1], PrecisionEnum.COIN_PLACES)))
            index += 1
        else:
            result.append((d, Decimal()))
    return result


def get_suggest_user_balance_series_points(user: User,
                                           account_type: AccountBalanceType = None,
                                           max_points=90) -> int:
    # 2020-10-17开始才有持仓历史数据, 2021-03-19开始才有AMM历史持仓数据
    earliest = ********** if account_type == AccountBalanceType.AMM else **********
    start = max(earliest, int(user.created_at.timestamp()))
    days = (today_timestamp_utc() - start) // 86400
    return min(max(days, 7), max_points)


def get_user_spot_balance_series(user_id: int,
                                 asset: str,
                                 start_date: date,
                                 end_date=None,
                                 is_format=True
                                 ) -> List[Tuple[date, Decimal]]:
    points = _get_points_by(start_date, end_date)
    idx = ExchangeLogDB.user_slice_balance_hash(user_id)
    table = ExchangeLogDB.user_slice_balance_table(idx)
    where_clause = f"user_id={user_id} and asset='{asset}' and "\
                   f"account={SPOT_ACCOUNT_ID} and report_date>='{start_date}'"
    if end_date:
        where_clause += f" and report_date<='{end_date}'"
    result = table.select("report_date, balance",
                          where=where_clause,
                          order_by="report_date")
    return _format_series(result, start_date, points) if is_format else result


def get_user_investment_balance_series(user_id: int,
                                       asset: str,
                                       start_date: date,
                                       end_date=None) -> List[Tuple[date, Decimal]]:
    """获取活期理财的币种余额曲线"""
    points = _get_points_by(start_date, end_date)
    idx = ExchangeLogDB.user_slice_balance_hash(user_id)
    table = ExchangeLogDB.user_slice_balance_table(idx)
    where_clause = f"user_id={user_id} and asset='{asset}' and "\
                   f"account={AssetInvestmentConfig.ACCOUNT_ID} and report_date>='{start_date}'"
    if end_date:
        where_clause += f" and report_date<='{end_date}'"
    select_fields = ("report_date", "balance-lock_balance as balance")
    result = table.select(*select_fields,
                          where=where_clause,
                          order_by="report_date")
    return _format_series(result, start_date, points)


def get_user_staking_balance_series(user_id: int,
                                       asset: str,
                                       start_date: date,
                                       end_date=None) -> List[Tuple[date, Decimal]]:
    points = _get_points_by(start_date, end_date)
    idx = ExchangeLogDB.user_slice_balance_hash(user_id)
    table = ExchangeLogDB.user_slice_balance_table(idx)
    where_clause = f"user_id={user_id} and asset='{asset}' and "\
                   f"account={StakingAccount.ACCOUNT_ID} and report_date>='{start_date}'"
    if end_date:
        where_clause += f" and report_date<='{end_date}'"
    result = table.select("report_date, balance",
                          where=where_clause,
                          order_by="report_date")
    return _format_series(result, start_date, points)


def get_user_margin_balance_series(user_id: int,
                                   account_id: int,
                                   start_date: date,
                                   end_date=None) -> List[Tuple[date, Decimal]]:
    points = _get_points_by(start_date, end_date)
    idx = ExchangeLogDB.user_slice_balance_hash(user_id)
    table = ExchangeLogDB.user_slice_balance_table(idx)
    where_clause = f"user_id={user_id} and account={account_id} and "\
                   f"report_date>='{start_date}'"
    if end_date:
        where_clause += f" and report_date<='{end_date}'"
    result = table.select("report_date, sum(market_value - margin_unflat_amount_usd)",
                          where=where_clause,
                          group_by="report_date",
                          order_by="report_date")
    return _format_series(result, start_date, points)


def get_user_perpetual_balance_series(user_id: int,
                                      asset: str,
                                      start_date: date,
                                      end_date=None) -> List[Tuple[date, Decimal]]:
    points = _get_points_by(start_date, end_date)
    idx = ExchangeLogDB.user_slice_perpetual_balance_hash(user_id)
    table = ExchangeLogDB.user_slice_perpetual_balance_table(idx)
    where_clause = f"user_id={user_id} and asset='{asset}' and "\
                   f"report_date>='{start_date}'"
    if end_date:
        where_clause += f" and report_date<='{end_date}'"
    result = table.select("report_date, balance",
                          where=where_clause,
                          order_by="report_date")
    return _format_series(result, start_date, points)


def get_user_amm_balance_series(user_id: int,
                                market: str,
                                points: int = 90) -> List[Tuple[date, Decimal]]:
    start_date = today() - timedelta(days=points)
    result = UserLiquiditySlice.query.filter(
        UserLiquiditySlice.user_id == user_id,
        UserLiquiditySlice.market == market,
        UserLiquiditySlice.date >= start_date
    ).order_by(
        UserLiquiditySlice.date
    ).with_entities(
        UserLiquiditySlice.date,
        UserLiquiditySlice.liquidity_usd
    ).all()
    return _format_series(result, start_date, points)


def get_user_spot_balance_sum_series(user_id: int,
                                     start_date: date,
                                     end_date=None
                                     ) -> List[Tuple[date, Decimal]]:
    points = _get_points_by(start_date, end_date)
    idx = ExchangeLogDB.user_slice_account_balance_sum_hash(user_id)
    table = ExchangeLogDB.user_slice_account_balance_sum_table(idx)
    where_clause = f"user_id={user_id} and account_type='{AccountBalanceType.SPOT.name}'" \
                   f" and report_date>='{start_date}'"
    if end_date:
        where_clause += f" and report_date<='{end_date}'"
    result = table.select("report_date, balance_usd",
                          where=where_clause,
                          order_by="report_date")
    return _format_series(result, start_date, points)


def get_user_amm_balance_sum_series(user_id: int,
                                    start_date: date,
                                    end_date=None
                                    ) -> List[Tuple[date, Decimal]]:
    points = _get_points_by(start_date, end_date)
    idx = ExchangeLogDB.user_slice_account_balance_sum_hash(user_id)
    table = ExchangeLogDB.user_slice_account_balance_sum_table(idx)
    where_clause = f"user_id={user_id} and account_type='{AccountBalanceType.AMM.name}'" \
                   f" and report_date>='{start_date}'"
    if end_date:
        where_clause += f" and report_date<='{end_date}'"
    result = table.select("report_date, balance_usd",
                          where=where_clause,
                          order_by="report_date")
    return _format_series(result, start_date, points)


def _get_account_sum_series(
        accounts: list[int],
        start_date: date,
        end_date: date = None,
) -> list[tuple[date, Decimal]]:
    points = _get_points_by(start_date, end_date)
    indexes = defaultdict(set)
    for user_id in accounts:
        idx = ExchangeLogDB.user_slice_account_balance_sum_hash(user_id)
        indexes[idx].add(user_id)

    result = defaultdict(Decimal)
    for idx, user_ids in indexes.items():
        table = ExchangeLogDB.user_slice_account_balance_sum_table(idx)
        where_clause = f"user_id in ({','.join(map(str, user_ids))}) and report_date>='{start_date}'"
        if end_date:
            where_clause += f" and report_date<='{end_date}'"
        r = table.select("report_date, sum(balance_usd)",
                         where=where_clause,
                         group_by="report_date",
                         order_by="report_date")
        for d, balance in r:
            result[d] += balance

    return _format_series(sorted(result.items(), key=lambda x: x[0]), start_date, points)


def get_user_sub_account_sum_series(
        user_id: int,
        start_date: date,
        end_date: date = None,
) -> list[tuple[date, Decimal]]:
    user: User = User.query.get(user_id)
    if user.is_sub_account:
        return []
    sub_accounts = [
        item for item, in SubAccount.query.with_entities(
            SubAccount.user_id,
        ).filter(
            SubAccount.main_user_id == user_id,
        ).all()
    ]
    return _get_account_sum_series(sub_accounts, start_date, end_date)


def get_user_balance_sum_series(
        user_id: int,
        start_date: date,
        end_date=None,
) -> list[tuple[date, Decimal]]:
    user: User = User.query.get(user_id)
    sub_accounts = []
    if not user.is_sub_account:
        sub_accounts = [
            item for item, in SubAccount.query.with_entities(
                SubAccount.user_id,
            ).filter(
                SubAccount.main_user_id == user_id,
            ).all()
        ]
    sub_accounts.append(user_id)
    return _get_account_sum_series(sub_accounts, start_date, end_date)


def _get_points_by(start_date: date, end_date=None) -> int:
    end_date = end_date if end_date else today()
    points = (end_date - start_date).days
    return points + 1
